#!/usr/bin/env python3
"""
PDF Textract Bedrock Processor

This script:
1. Takes a PDF file path as input
2. Splits the PDF into individual pages
3. Processes each page with AWS Textract (sync method) in parallel
4. Merges all page results with <page1></page1> tags
5. Uses AWS Bedrock Converse API with OpenAI models to analyze document types

Usage:
    python pdf_textract_bedrock_processor.py /path/to/document.pdf
"""

import os
import sys
import json
import logging
import tempfile
import concurrent.futures
from pathlib import Path
from typing import List, Dict, Tuple

import boto3
import PyPDF2
from botocore.exceptions import ClientError


def setup_logging() -> logging.Logger:
    """Setup logging configuration."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)


def split_pdf(pdf_path: str, output_dir: str) -> List[str]:
    """
    Split PDF into individual pages and save them as separate PDF files.
    
    Args:
        pdf_path: Path to the input PDF file
        output_dir: Directory to save individual page PDFs
        
    Returns:
        List of paths to individual page PDF files
    """
    page_files = []
    
    with open(pdf_path, 'rb') as file:
        pdf_reader = PyPDF2.PdfReader(file)
        total_pages = len(pdf_reader.pages)
        
        for page_num in range(total_pages):
            # Create a new PDF writer for each page
            pdf_writer = PyPDF2.PdfWriter()
            pdf_writer.add_page(pdf_reader.pages[page_num])
            
            # Save individual page
            page_filename = f"page_{page_num + 1:03d}.pdf"
            page_path = os.path.join(output_dir, page_filename)
            
            with open(page_path, 'wb') as output_file:
                pdf_writer.write(output_file)
            
            page_files.append(page_path)
    
    return page_files


def process_page_with_textract(page_path: str, s3_client, textract_client, bucket_name: str, temp_prefix: str) -> Tuple[int, str]:
    """
    Process a single PDF page with AWS Textract (sync method).
    
    Args:
        page_path: Path to the PDF page file
        s3_client: AWS S3 client
        textract_client: AWS Textract client
        bucket_name: S3 bucket name for temporary uploads
        temp_prefix: S3 prefix for temporary files
        
    Returns:
        Tuple of (page_number, extracted_text)
    """
    try:
        # Extract page number from filename
        page_filename = os.path.basename(page_path)
        page_num = int(page_filename.split('_')[1].split('.')[0])
        
        # Upload to S3
        s3_key = f"{temp_prefix}/{page_filename}"
        s3_client.upload_file(page_path, bucket_name, s3_key)
        
        # Process with Textract (sync method for single pages)
        response = textract_client.detect_document_text(
            Document={
                'S3Object': {
                    'Bucket': bucket_name,
                    'Name': s3_key
                }
            }
        )
        
        # Extract text from response
        text_lines = []
        for block in response.get('Blocks', []):
            if block['BlockType'] == 'LINE':
                text_lines.append(block.get('Text', ''))
        
        extracted_text = '\n'.join(text_lines)
        
        # Clean up S3 file
        try:
            s3_client.delete_object(Bucket=bucket_name, Key=s3_key)
        except Exception:
            pass  # Ignore cleanup errors
        
        return page_num, extracted_text
        
    except Exception as e:
        logging.error(f"Error processing page {page_path}: {e}")
        return 0, f"Error processing page: {e}"


def process_pdf_pages_parallel(page_files: List[str], bucket_name: str, temp_prefix: str = "temp-textract", max_workers: int = 5) -> str:
    """
    Process multiple PDF pages with Textract in parallel.
    
    Args:
        page_files: List of paths to individual page PDF files
        bucket_name: S3 bucket name for temporary uploads
        temp_prefix: S3 prefix for temporary files
        max_workers: Maximum number of parallel workers
        
    Returns:
        Combined text with page tags
    """
    # Initialize AWS clients
    s3_client = boto3.client('s3')
    textract_client = boto3.client('textract')
    
    # Process pages in parallel
    page_results = {}
    
    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        # Submit all tasks
        future_to_page = {
            executor.submit(process_page_with_textract, page_path, s3_client, textract_client, bucket_name, temp_prefix): page_path
            for page_path in page_files
        }
        
        # Collect results
        for future in concurrent.futures.as_completed(future_to_page):
            page_path = future_to_page[future]
            try:
                page_num, extracted_text = future.result()
                page_results[page_num] = extracted_text
                logging.info(f"Completed processing page {page_num}")
            except Exception as e:
                logging.error(f"Error processing {page_path}: {e}")
    
    # Combine results in page order
    combined_text_parts = []
    for page_num in sorted(page_results.keys()):
        if page_num > 0:  # Skip error pages (page_num = 0)
            page_text = page_results[page_num]
            combined_text_parts.append(f"<page{page_num}>\n{page_text}\n</page{page_num}>")
    
    return '\n\n'.join(combined_text_parts)


def analyze_document_types_with_bedrock(combined_text: str, region: str = 'us-east-1') -> Dict:
    """
    Analyze document types using AWS Bedrock Converse API with OpenAI models.
    
    Args:
        combined_text: Combined text from all pages with page tags
        region: AWS region for Bedrock
        
    Returns:
        Dictionary containing document type analysis results
    """
    bedrock_client = boto3.client('bedrock-runtime', region_name=region)
    
    # System prompt for document type analysis
    system_prompt = """You are an expert document classifier. Analyze the provided text from a multi-page document and identify the document type for each page.

For each page, determine if it contains:
- Invoice
- Receipt  
- Purchase Order
- Delivery Note
- Contract
- Letter
- Form
- Other (specify)

Return your analysis as a JSON object with the following structure:
{
    "overall_document_type": "primary document type",
    "pages": {
        "page1": {"type": "document_type", "confidence": 0.95, "key_indicators": ["list", "of", "indicators"]},
        "page2": {"type": "document_type", "confidence": 0.90, "key_indicators": ["list", "of", "indicators"]},
        ...
    },
    "summary": "Brief summary of the document content and structure"
}"""

    # User message with the combined text
    user_message = f"Please analyze this multi-page document and classify each page:\n\n{combined_text}"
    
    # Prepare messages for Bedrock
    messages = [
        {
            "role": "user", 
            "content": [{"text": user_message}]
        }
    ]
    
    # Try OpenAI models available on Bedrock
    model_ids = [
        "anthropic.claude-3-5-sonnet-20241022-v2:0"  # Fallback to Claude
    ]
    
    for model_id in model_ids:
        try:
            logging.info(f"Trying Bedrock model: {model_id}")
            response = bedrock_client.converse(
                modelId=model_id,
                system=[{"text": system_prompt}],
                messages=messages,
                inferenceConfig={
                    'maxTokens': 2000,
                    'temperature': 0.1,
                    'topP': 0.9,
                }
            )
            
            # Extract response text
            content = response['output']['message']['content']
            if content and 'text' in content[0]:
                response_text = content[0]['text']
                
                # Parse JSON response
                try:
                    analysis_result = json.loads(response_text)
                    logging.info(f"Successfully analyzed document types using {model_id}")
                    return analysis_result
                except json.JSONDecodeError:
                    # Try to extract JSON from response
                    import re
                    json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
                    if json_match:
                        analysis_result = json.loads(json_match.group())
                        return analysis_result
                    else:
                        logging.warning(f"Could not parse JSON from {model_id} response")
                        continue
            
        except ClientError as e:
            logging.warning(f"Model {model_id} failed: {e}")
            if model_id == model_ids[-1]:  # Last model
                raise
            continue
    
    raise Exception("All Bedrock models failed to analyze document types")


def main():
    """Main function to process PDF with Textract and Bedrock."""
    if len(sys.argv) != 2:
        print("Usage: python pdf_textract_bedrock_processor.py <pdf_file_path>")
        sys.exit(1)
    
    pdf_path = sys.argv[1]
    
    # Validate input
    if not os.path.exists(pdf_path):
        print(f"Error: PDF file not found: {pdf_path}")
        sys.exit(1)
    
    if not pdf_path.lower().endswith('.pdf'):
        print("Error: Input file must be a PDF")
        sys.exit(1)
    
    # Setup logging
    logger = setup_logging()
    logger.info(f"Processing PDF: {pdf_path}")
    
    # Configuration
    BUCKET_NAME = "document-extraction-logistically"  # Update with your bucket
    TEMP_PREFIX = "temp-pdf-processing"
    
    try:
        # Create temporary directory for page files
        with tempfile.TemporaryDirectory() as temp_dir:
            logger.info("Splitting PDF into individual pages...")
            page_files = split_pdf(pdf_path, temp_dir)
            logger.info(f"Split PDF into {len(page_files)} pages")
            
            # Process pages with Textract in parallel
            logger.info("Processing pages with Textract in parallel...")
            combined_text = process_pdf_pages_parallel(page_files, BUCKET_NAME, TEMP_PREFIX)
            
            # Analyze document types with Bedrock
            logger.info("Analyzing document types with Bedrock...")
            analysis_result = analyze_document_types_with_bedrock(combined_text)
            
            # Output results
            print("\n" + "="*50)
            print("DOCUMENT TYPE ANALYSIS RESULTS")
            print("="*50)
            print(json.dumps(analysis_result, indent=2))
            
            # Optionally save results to file
            output_file = f"{Path(pdf_path).stem}_analysis.json"
            with open(output_file, 'w') as f:
                json.dump({
                    'source_file': pdf_path,
                    'analysis': analysis_result,
                    'combined_text': combined_text
                }, f, indent=2)
            
            logger.info(f"Results saved to: {output_file}")
            
    except Exception as e:
        logger.error(f"Processing failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
